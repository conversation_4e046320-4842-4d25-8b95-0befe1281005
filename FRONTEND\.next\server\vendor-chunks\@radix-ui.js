"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFZJQ1RVU1xcRGVza3RvcFxcdGVzdF93ZWJcXHRlc3Rfd2ViXFxGUk9OVEVORFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHByaW1pdGl2ZVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // packages/react/avatar/src/Avatar.tsx\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)({\n        \"AvatarImage.useCallbackRef[handleLoadingStatusChange]\": (status)=>{\n            onLoadingStatusChange(status);\n            context.onImageLoadingStatusChange(status);\n        }\n    }[\"AvatarImage.useCallbackRef[handleLoadingStatusChange]\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"AvatarImage.useLayoutEffect\": ()=>{\n            if (imageLoadingStatus !== \"idle\") {\n                handleLoadingStatusChange(imageLoadingStatus);\n            }\n        }\n    }[\"AvatarImage.useLayoutEffect\"], [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"AvatarFallback.useEffect\": ()=>{\n            if (delayMs !== void 0) {\n                const timerId = window.setTimeout({\n                    \"AvatarFallback.useEffect.timerId\": ()=>setCanRender(true)\n                }[\"AvatarFallback.useEffect.timerId\"], delayMs);\n                return ({\n                    \"AvatarFallback.useEffect\": ()=>window.clearTimeout(timerId)\n                })[\"AvatarFallback.useEffect\"];\n            }\n        }\n    }[\"AvatarFallback.useEffect\"], [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction useImageLoadingStatus(src, referrerPolicy) {\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            if (!src) {\n                setLoadingStatus(\"error\");\n                return;\n            }\n            let isMounted = true;\n            const image = new window.Image();\n            const updateStatus = {\n                \"useImageLoadingStatus.useLayoutEffect.updateStatus\": (status)=>({\n                        \"useImageLoadingStatus.useLayoutEffect.updateStatus\": ()=>{\n                            if (!isMounted) return;\n                            setLoadingStatus(status);\n                        }\n                    })[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"]\n            }[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"];\n            setLoadingStatus(\"loading\");\n            image.onload = updateStatus(\"loaded\");\n            image.onerror = updateStatus(\"error\");\n            image.src = src;\n            if (referrerPolicy) {\n                image.referrerPolicy = referrerPolicy;\n            }\n            return ({\n                \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"useImageLoadingStatus.useLayoutEffect\"];\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        src,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWF2YXRhci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNZO0FBQ0o7QUFDQztBQUNOO0FBb0NsQjtBQTVCUixJQUFNLGNBQWM7QUFHcEIsSUFBTSxDQUFDLHFCQUFxQixpQkFBaUIsSUFBSSwyRUFBa0IsQ0FBQyxXQUFXO0FBUy9FLElBQU0sQ0FBQyxnQkFBZ0IsZ0JBQWdCLElBQUksb0JBQXdDLFdBQVc7QUFNOUYsSUFBTSx1QkFBZSw4Q0FDbkIsQ0FBQyxPQUFpQztJQUNoQyxNQUFNLEVBQUUsZUFBZSxHQUFHLFlBQVksSUFBSTtJQUMxQyxNQUFNLENBQUMsb0JBQW9CLHFCQUFxQixJQUFVLDRDQUE2QixNQUFNO0lBQzdGLE9BQ0UsdUVBQUM7UUFDQyxPQUFPO1FBQ1A7UUFDQSw0QkFBNEI7UUFFNUIsaUZBQUMsZ0VBQVMsQ0FBQyxNQUFWO1lBQWdCLEdBQUc7WUFBYSxLQUFLO1FBQUEsQ0FBYztJQUFBO0FBRzFEO0FBR0YsT0FBTyxjQUFjO0FBTXJCLElBQU0sYUFBYTtBQVFuQixJQUFNLDRCQUFvQiw4Q0FDeEIsQ0FBQyxPQUFzQztJQUNyQyxNQUFNLEVBQUUsZUFBZSxLQUFLLHdCQUF3QixLQUFPLEdBQUcsR0FBRyxXQUFXLElBQUk7SUFDaEYsTUFBTSxVQUFVLGlCQUFpQixZQUFZLGFBQWE7SUFDMUQsTUFBTSxxQkFBcUIsc0JBQXNCLEtBQUssV0FBVyxjQUFjO0lBQy9FLE1BQU0sNEJBQTRCLGdGQUFjO2lFQUFDLENBQUM7WUFDaEQsc0JBQXNCLE1BQU07WUFDNUIsUUFBUSwyQkFBMkIsTUFBTTtRQUMzQyxDQUFDOztJQUVELGtGQUFlO3VDQUFDO1lBQ2QsSUFBSSx1QkFBdUIsUUFBUTtnQkFDakMsMEJBQTBCLGtCQUFrQjtZQUM5QztRQUNGO3NDQUFHO1FBQUM7UUFBb0IseUJBQXlCO0tBQUM7SUFFbEQsT0FBTyx1QkFBdUIsV0FDNUIsdUVBQUMsZ0VBQVMsQ0FBQyxLQUFWO1FBQWUsR0FBRztRQUFZLEtBQUs7UUFBYztJQUFBLENBQVUsSUFDMUQ7QUFDTjtBQUdGLFlBQVksY0FBYztBQU0xQixJQUFNLGdCQUFnQjtBQU90QixJQUFNLCtCQUF1Qiw4Q0FDM0IsQ0FBQyxPQUF5QztJQUN4QyxNQUFNLEVBQUUsZUFBZSxTQUFTLEdBQUcsY0FBYyxJQUFJO0lBQ3JELE1BQU0sVUFBVSxpQkFBaUIsZUFBZSxhQUFhO0lBQzdELE1BQU0sQ0FBQyxXQUFXLFlBQVksSUFBVSw0Q0FBUyxZQUFZLE1BQVM7SUFFaEU7b0NBQVU7WUFDZCxJQUFJLFlBQVksUUFBVztnQkFDekIsTUFBTSxVQUFVLE9BQU87d0RBQVcsSUFBTSxhQUFhLElBQUk7dURBQUcsT0FBTztnQkFDbkU7Z0RBQU8sSUFBTSxPQUFPLGFBQWEsT0FBTzs7WUFDMUM7UUFDRjttQ0FBRztRQUFDLE9BQU87S0FBQztJQUVaLE9BQU8sYUFBYSxRQUFRLHVCQUF1QixXQUNqRCx1RUFBQyxnRUFBUyxDQUFDLE1BQVY7UUFBZ0IsR0FBRztRQUFlLEtBQUs7SUFBQSxDQUFjLElBQ3BEO0FBQ047QUFHRixlQUFlLGNBQWM7QUFJN0IsU0FBUyxzQkFBc0IsS0FBYyxnQkFBb0Q7SUFDL0YsTUFBTSxDQUFDLGVBQWUsZ0JBQWdCLElBQVUsNENBQTZCLE1BQU07SUFFbkYsa0ZBQWU7aURBQUM7WUFDZCxJQUFJLENBQUMsS0FBSztnQkFDUixpQkFBaUIsT0FBTztnQkFDeEI7WUFDRjtZQUVBLElBQUksWUFBWTtZQUNoQixNQUFNLFFBQVEsSUFBSSxPQUFPLE1BQU07WUFFL0IsTUFBTTtzRUFBZSxDQUFDOzhFQUErQjs0QkFDbkQsSUFBSSxDQUFDLFVBQVc7NEJBQ2hCLGlCQUFpQixNQUFNO3dCQUN6Qjs7O1lBRUEsaUJBQWlCLFNBQVM7WUFDMUIsTUFBTSxTQUFTLGFBQWEsUUFBUTtZQUNwQyxNQUFNLFVBQVUsYUFBYSxPQUFPO1lBQ3BDLE1BQU0sTUFBTTtZQUNaLElBQUksZ0JBQWdCO2dCQUNsQixNQUFNLGlCQUFpQjtZQUN6QjtZQUVBO3lEQUFPO29CQUNMLFlBQVk7Z0JBQ2Q7O1FBQ0Y7Z0RBQUc7UUFBQztRQUFLLGNBQWM7S0FBQztJQUV4QixPQUFPO0FBQ1Q7QUFDQSxJQUFNLE9BQU87QUFDYixJQUFNLFFBQVE7QUFDZCxJQUFNLFdBQVciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVklDVFVTXFxEZXNrdG9wXFx0ZXN0X3dlYlxcdGVzdF93ZWJcXHNyY1xcQXZhdGFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0U2NvcGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29udGV4dCc7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmJztcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdCc7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcblxuaW1wb3J0IHR5cGUgeyBTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogQXZhdGFyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEFWQVRBUl9OQU1FID0gJ0F2YXRhcic7XG5cbnR5cGUgU2NvcGVkUHJvcHM8UD4gPSBQICYgeyBfX3Njb3BlQXZhdGFyPzogU2NvcGUgfTtcbmNvbnN0IFtjcmVhdGVBdmF0YXJDb250ZXh0LCBjcmVhdGVBdmF0YXJTY29wZV0gPSBjcmVhdGVDb250ZXh0U2NvcGUoQVZBVEFSX05BTUUpO1xuXG50eXBlIEltYWdlTG9hZGluZ1N0YXR1cyA9ICdpZGxlJyB8ICdsb2FkaW5nJyB8ICdsb2FkZWQnIHwgJ2Vycm9yJztcblxudHlwZSBBdmF0YXJDb250ZXh0VmFsdWUgPSB7XG4gIGltYWdlTG9hZGluZ1N0YXR1czogSW1hZ2VMb2FkaW5nU3RhdHVzO1xuICBvbkltYWdlTG9hZGluZ1N0YXR1c0NoYW5nZShzdGF0dXM6IEltYWdlTG9hZGluZ1N0YXR1cyk6IHZvaWQ7XG59O1xuXG5jb25zdCBbQXZhdGFyUHJvdmlkZXIsIHVzZUF2YXRhckNvbnRleHRdID0gY3JlYXRlQXZhdGFyQ29udGV4dDxBdmF0YXJDb250ZXh0VmFsdWU+KEFWQVRBUl9OQU1FKTtcblxudHlwZSBBdmF0YXJFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLnNwYW4+O1xudHlwZSBQcmltaXRpdmVTcGFuUHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5zcGFuPjtcbmludGVyZmFjZSBBdmF0YXJQcm9wcyBleHRlbmRzIFByaW1pdGl2ZVNwYW5Qcm9wcyB7fVxuXG5jb25zdCBBdmF0YXIgPSBSZWFjdC5mb3J3YXJkUmVmPEF2YXRhckVsZW1lbnQsIEF2YXRhclByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxBdmF0YXJQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZUF2YXRhciwgLi4uYXZhdGFyUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IFtpbWFnZUxvYWRpbmdTdGF0dXMsIHNldEltYWdlTG9hZGluZ1N0YXR1c10gPSBSZWFjdC51c2VTdGF0ZTxJbWFnZUxvYWRpbmdTdGF0dXM+KCdpZGxlJyk7XG4gICAgcmV0dXJuIChcbiAgICAgIDxBdmF0YXJQcm92aWRlclxuICAgICAgICBzY29wZT17X19zY29wZUF2YXRhcn1cbiAgICAgICAgaW1hZ2VMb2FkaW5nU3RhdHVzPXtpbWFnZUxvYWRpbmdTdGF0dXN9XG4gICAgICAgIG9uSW1hZ2VMb2FkaW5nU3RhdHVzQ2hhbmdlPXtzZXRJbWFnZUxvYWRpbmdTdGF0dXN9XG4gICAgICA+XG4gICAgICAgIDxQcmltaXRpdmUuc3BhbiB7Li4uYXZhdGFyUHJvcHN9IHJlZj17Zm9yd2FyZGVkUmVmfSAvPlxuICAgICAgPC9BdmF0YXJQcm92aWRlcj5cbiAgICApO1xuICB9XG4pO1xuXG5BdmF0YXIuZGlzcGxheU5hbWUgPSBBVkFUQVJfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogQXZhdGFySW1hZ2VcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgSU1BR0VfTkFNRSA9ICdBdmF0YXJJbWFnZSc7XG5cbnR5cGUgQXZhdGFySW1hZ2VFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLmltZz47XG50eXBlIFByaW1pdGl2ZUltYWdlUHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5pbWc+O1xuaW50ZXJmYWNlIEF2YXRhckltYWdlUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVJbWFnZVByb3BzIHtcbiAgb25Mb2FkaW5nU3RhdHVzQ2hhbmdlPzogKHN0YXR1czogSW1hZ2VMb2FkaW5nU3RhdHVzKSA9PiB2b2lkO1xufVxuXG5jb25zdCBBdmF0YXJJbWFnZSA9IFJlYWN0LmZvcndhcmRSZWY8QXZhdGFySW1hZ2VFbGVtZW50LCBBdmF0YXJJbWFnZVByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxBdmF0YXJJbWFnZVByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlQXZhdGFyLCBzcmMsIG9uTG9hZGluZ1N0YXR1c0NoYW5nZSA9ICgpID0+IHt9LCAuLi5pbWFnZVByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlQXZhdGFyQ29udGV4dChJTUFHRV9OQU1FLCBfX3Njb3BlQXZhdGFyKTtcbiAgICBjb25zdCBpbWFnZUxvYWRpbmdTdGF0dXMgPSB1c2VJbWFnZUxvYWRpbmdTdGF0dXMoc3JjLCBpbWFnZVByb3BzLnJlZmVycmVyUG9saWN5KTtcbiAgICBjb25zdCBoYW5kbGVMb2FkaW5nU3RhdHVzQ2hhbmdlID0gdXNlQ2FsbGJhY2tSZWYoKHN0YXR1czogSW1hZ2VMb2FkaW5nU3RhdHVzKSA9PiB7XG4gICAgICBvbkxvYWRpbmdTdGF0dXNDaGFuZ2Uoc3RhdHVzKTtcbiAgICAgIGNvbnRleHQub25JbWFnZUxvYWRpbmdTdGF0dXNDaGFuZ2Uoc3RhdHVzKTtcbiAgICB9KTtcblxuICAgIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICBpZiAoaW1hZ2VMb2FkaW5nU3RhdHVzICE9PSAnaWRsZScpIHtcbiAgICAgICAgaGFuZGxlTG9hZGluZ1N0YXR1c0NoYW5nZShpbWFnZUxvYWRpbmdTdGF0dXMpO1xuICAgICAgfVxuICAgIH0sIFtpbWFnZUxvYWRpbmdTdGF0dXMsIGhhbmRsZUxvYWRpbmdTdGF0dXNDaGFuZ2VdKTtcblxuICAgIHJldHVybiBpbWFnZUxvYWRpbmdTdGF0dXMgPT09ICdsb2FkZWQnID8gKFxuICAgICAgPFByaW1pdGl2ZS5pbWcgey4uLmltYWdlUHJvcHN9IHJlZj17Zm9yd2FyZGVkUmVmfSBzcmM9e3NyY30gLz5cbiAgICApIDogbnVsbDtcbiAgfVxuKTtcblxuQXZhdGFySW1hZ2UuZGlzcGxheU5hbWUgPSBJTUFHRV9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBBdmF0YXJGYWxsYmFja1xuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBGQUxMQkFDS19OQU1FID0gJ0F2YXRhckZhbGxiYWNrJztcblxudHlwZSBBdmF0YXJGYWxsYmFja0VsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuc3Bhbj47XG5pbnRlcmZhY2UgQXZhdGFyRmFsbGJhY2tQcm9wcyBleHRlbmRzIFByaW1pdGl2ZVNwYW5Qcm9wcyB7XG4gIGRlbGF5TXM/OiBudW1iZXI7XG59XG5cbmNvbnN0IEF2YXRhckZhbGxiYWNrID0gUmVhY3QuZm9yd2FyZFJlZjxBdmF0YXJGYWxsYmFja0VsZW1lbnQsIEF2YXRhckZhbGxiYWNrUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPEF2YXRhckZhbGxiYWNrUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVBdmF0YXIsIGRlbGF5TXMsIC4uLmZhbGxiYWNrUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VBdmF0YXJDb250ZXh0KEZBTExCQUNLX05BTUUsIF9fc2NvcGVBdmF0YXIpO1xuICAgIGNvbnN0IFtjYW5SZW5kZXIsIHNldENhblJlbmRlcl0gPSBSZWFjdC51c2VTdGF0ZShkZWxheU1zID09PSB1bmRlZmluZWQpO1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChkZWxheU1zICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY29uc3QgdGltZXJJZCA9IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHNldENhblJlbmRlcih0cnVlKSwgZGVsYXlNcyk7XG4gICAgICAgIHJldHVybiAoKSA9PiB3aW5kb3cuY2xlYXJUaW1lb3V0KHRpbWVySWQpO1xuICAgICAgfVxuICAgIH0sIFtkZWxheU1zXSk7XG5cbiAgICByZXR1cm4gY2FuUmVuZGVyICYmIGNvbnRleHQuaW1hZ2VMb2FkaW5nU3RhdHVzICE9PSAnbG9hZGVkJyA/IChcbiAgICAgIDxQcmltaXRpdmUuc3BhbiB7Li4uZmFsbGJhY2tQcm9wc30gcmVmPXtmb3J3YXJkZWRSZWZ9IC8+XG4gICAgKSA6IG51bGw7XG4gIH1cbik7XG5cbkF2YXRhckZhbGxiYWNrLmRpc3BsYXlOYW1lID0gRkFMTEJBQ0tfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5mdW5jdGlvbiB1c2VJbWFnZUxvYWRpbmdTdGF0dXMoc3JjPzogc3RyaW5nLCByZWZlcnJlclBvbGljeT86IFJlYWN0LkhUTUxBdHRyaWJ1dGVSZWZlcnJlclBvbGljeSkge1xuICBjb25zdCBbbG9hZGluZ1N0YXR1cywgc2V0TG9hZGluZ1N0YXR1c10gPSBSZWFjdC51c2VTdGF0ZTxJbWFnZUxvYWRpbmdTdGF0dXM+KCdpZGxlJyk7XG5cbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXNyYykge1xuICAgICAgc2V0TG9hZGluZ1N0YXR1cygnZXJyb3InKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBsZXQgaXNNb3VudGVkID0gdHJ1ZTtcbiAgICBjb25zdCBpbWFnZSA9IG5ldyB3aW5kb3cuSW1hZ2UoKTtcblxuICAgIGNvbnN0IHVwZGF0ZVN0YXR1cyA9IChzdGF0dXM6IEltYWdlTG9hZGluZ1N0YXR1cykgPT4gKCkgPT4ge1xuICAgICAgaWYgKCFpc01vdW50ZWQpIHJldHVybjtcbiAgICAgIHNldExvYWRpbmdTdGF0dXMoc3RhdHVzKTtcbiAgICB9O1xuXG4gICAgc2V0TG9hZGluZ1N0YXR1cygnbG9hZGluZycpO1xuICAgIGltYWdlLm9ubG9hZCA9IHVwZGF0ZVN0YXR1cygnbG9hZGVkJyk7XG4gICAgaW1hZ2Uub25lcnJvciA9IHVwZGF0ZVN0YXR1cygnZXJyb3InKTtcbiAgICBpbWFnZS5zcmMgPSBzcmM7XG4gICAgaWYgKHJlZmVycmVyUG9saWN5KSB7XG4gICAgICBpbWFnZS5yZWZlcnJlclBvbGljeSA9IHJlZmVycmVyUG9saWN5O1xuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpc01vdW50ZWQgPSBmYWxzZTtcbiAgICB9O1xuICB9LCBbc3JjLCByZWZlcnJlclBvbGljeV0pO1xuXG4gIHJldHVybiBsb2FkaW5nU3RhdHVzO1xufVxuY29uc3QgUm9vdCA9IEF2YXRhcjtcbmNvbnN0IEltYWdlID0gQXZhdGFySW1hZ2U7XG5jb25zdCBGYWxsYmFjayA9IEF2YXRhckZhbGxiYWNrO1xuXG5leHBvcnQge1xuICBjcmVhdGVBdmF0YXJTY29wZSxcbiAgLy9cbiAgQXZhdGFyLFxuICBBdmF0YXJJbWFnZSxcbiAgQXZhdGFyRmFsbGJhY2ssXG4gIC8vXG4gIFJvb3QsXG4gIEltYWdlLFxuICBGYWxsYmFjayxcbn07XG5leHBvcnQgdHlwZSB7IEF2YXRhclByb3BzLCBBdmF0YXJJbWFnZVByb3BzLCBBdmF0YXJGYWxsYmFja1Byb3BzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Checkbox,CheckboxIndicator,Indicator,Root,createCheckboxScope auto */ // packages/react/checkbox/src/Checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProvider, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nvar Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...checkboxProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Checkbox.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Checkbox.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked,\n        onChange: onCheckedChange\n    });\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Checkbox.useEffect\": ()=>{\n            const form2 = button?.form;\n            if (form2) {\n                const reset = {\n                    \"Checkbox.useEffect.reset\": ()=>setChecked(initialCheckedStateRef.current)\n                }[\"Checkbox.useEffect.reset\"];\n                form2.addEventListener(\"reset\", reset);\n                return ({\n                    \"Checkbox.useEffect\": ()=>form2.removeEventListener(\"reset\", reset)\n                })[\"Checkbox.useEffect\"];\n            }\n        }\n    }[\"Checkbox.useEffect\"], [\n        button,\n        setChecked\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(CheckboxProvider, {\n        scope: __scopeCheckbox,\n        state: checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"checkbox\",\n                \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...checkboxProps,\n                ref: composedRefs,\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                    if (event.key === \"Enter\") event.preventDefault();\n                }),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>isIndeterminate(prevChecked) ? true : !prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                },\n                defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked\n            })\n        ]\n    });\n});\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || isIndeterminate(context.state) || context.state === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n            \"data-state\": getState(context.state),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"none\",\n                ...props.style\n            }\n        })\n    });\n});\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BubbleInput = (props)=>{\n    const { control, checked, bubbles = true, defaultChecked, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                input.indeterminate = isIndeterminate(checked);\n                setChecked.call(input, isIndeterminate(checked) ? false : checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        ...inputProps,\n        tabIndex: -1,\n        ref,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n};\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Checkbox;\nvar Indicator = CheckboxIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection auto */ // packages/react/collection/src/Collection.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection.CollectionItemSlot.useEffect\": ()=>{\n                context.itemMap.set(ref, {\n                    ref,\n                    ...itemData\n                });\n                return ({\n                    \"createCollection.CollectionItemSlot.useEffect\": ()=>void context.itemMap.delete(ref)\n                })[\"createCollection.CollectionItemSlot.useEffect\"];\n            }\n        }[\"createCollection.CollectionItemSlot.useEffect\"]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"createCollection.useCollection.useCallback[getItems]\": ()=>{\n                const collectionNode = context.collectionRef.current;\n                if (!collectionNode) return [];\n                const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n                const items = Array.from(context.itemMap.values());\n                const orderedItems = items.sort({\n                    \"createCollection.useCollection.useCallback[getItems].orderedItems\": (a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n                }[\"createCollection.useCollection.useCallback[getItems].orderedItems\"]);\n                return orderedItems;\n            }\n        }[\"createCollection.useCollection.useCallback[getItems]\"], [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // packages/react/dialog/src/Dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Dialog.useCallback\": ()=>setOpen({\n                    \"Dialog.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Dialog.useCallback\"])\n        }[\"Dialog.useCallback\"], [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DialogContentModal.useEffect\": ()=>{\n            const content = contentRef.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n        }\n    }[\"DialogContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TitleWarning.useEffect\": ()=>{\n            if (titleId) {\n                const hasTitle = document.getElementById(titleId);\n                if (!hasTitle) console.error(MESSAGE);\n            }\n        }\n    }[\"TitleWarning.useEffect\"], [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DescriptionWarning.useEffect\": ()=>{\n            const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n            if (descriptionId && describedById) {\n                const hasDescription = document.getElementById(descriptionId);\n                if (!hasDescription) console.warn(MESSAGE);\n            }\n        }\n    }[\"DescriptionWarning.useEffect\"], [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/Direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVklDVFVTXFxEZXNrdG9wXFx0ZXN0X3dlYlxcdGVzdF93ZWJcXEZST05URU5EXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtZGlyZWN0aW9uXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvZGlyZWN0aW9uL3NyYy9EaXJlY3Rpb24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIERpcmVjdGlvbkNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHZvaWQgMCk7XG52YXIgRGlyZWN0aW9uUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBkaXIsIGNoaWxkcmVuIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRGlyZWN0aW9uQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZGlyLCBjaGlsZHJlbiB9KTtcbn07XG5mdW5jdGlvbiB1c2VEaXJlY3Rpb24obG9jYWxEaXIpIHtcbiAgY29uc3QgZ2xvYmFsRGlyID0gUmVhY3QudXNlQ29udGV4dChEaXJlY3Rpb25Db250ZXh0KTtcbiAgcmV0dXJuIGxvY2FsRGlyIHx8IGdsb2JhbERpciB8fCBcImx0clwiO1xufVxudmFyIFByb3ZpZGVyID0gRGlyZWN0aW9uUHJvdmlkZXI7XG5leHBvcnQge1xuICBEaXJlY3Rpb25Qcm92aWRlcixcbiAgUHJvdmlkZXIsXG4gIHVzZURpcmVjdGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/DismissableLayer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusGuards.useEffect\": ()=>{\n            const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n            document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n            document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n            count++;\n            return ({\n                \"useFocusGuards.useEffect\": ()=>{\n                    if (count === 1) {\n                        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach({\n                            \"useFocusGuards.useEffect\": (node)=>node.remove()\n                        }[\"useFocusGuards.useEffect\"]);\n                    }\n                    count--;\n                }\n            })[\"useFocusGuards.useEffect\"];\n        }\n    }[\"useFocusGuards.useEffect\"], []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWZvY3VzLWd1YXJkcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1QjtBQUd2QixJQUFJLFFBQVE7QUFFWixTQUFTLFlBQVksT0FBWTtJQUMvQixlQUFlO0lBQ2YsT0FBTyxNQUFNO0FBQ2Y7QUFNQSxTQUFTLGlCQUFpQjtJQUNsQjtvQ0FBVTtZQUNkLE1BQU0sYUFBYSxTQUFTLGlCQUFpQiwwQkFBMEI7WUFDdkUsU0FBUyxLQUFLLHNCQUFzQixjQUFjLFdBQVcsQ0FBQyxLQUFLLGlCQUFpQixDQUFDO1lBQ3JGLFNBQVMsS0FBSyxzQkFBc0IsYUFBYSxXQUFXLENBQUMsS0FBSyxpQkFBaUIsQ0FBQztZQUNwRjtZQUVBOzRDQUFPO29CQUNMLElBQUksVUFBVSxHQUFHO3dCQUNmLFNBQVMsaUJBQWlCLDBCQUEwQixFQUFFO3dEQUFRLENBQUMsT0FBUyxLQUFLLE9BQU8sQ0FBQzs7b0JBQ3ZGO29CQUNBO2dCQUNGOztRQUNGO21DQUFHLENBQUMsQ0FBQztBQUNQO0FBRUEsU0FBUyxtQkFBbUI7SUFDMUIsTUFBTSxVQUFVLFNBQVMsY0FBYyxNQUFNO0lBQzdDLFFBQVEsYUFBYSwwQkFBMEIsRUFBRTtJQUNqRCxRQUFRLFdBQVc7SUFDbkIsUUFBUSxNQUFNLFVBQVU7SUFDeEIsUUFBUSxNQUFNLFVBQVU7SUFDeEIsUUFBUSxNQUFNLFdBQVc7SUFDekIsUUFBUSxNQUFNLGdCQUFnQjtJQUM5QixPQUFPO0FBQ1Q7QUFFQSxJQUFNLE9BQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVklDVFVTXFxEZXNrdG9wXFx0ZXN0X3dlYlxcdGVzdF93ZWJcXHNyY1xcRm9jdXNHdWFyZHMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcblxuLyoqIE51bWJlciBvZiBjb21wb25lbnRzIHdoaWNoIGhhdmUgcmVxdWVzdGVkIGludGVyZXN0IHRvIGhhdmUgZm9jdXMgZ3VhcmRzICovXG5sZXQgY291bnQgPSAwO1xuXG5mdW5jdGlvbiBGb2N1c0d1YXJkcyhwcm9wczogYW55KSB7XG4gIHVzZUZvY3VzR3VhcmRzKCk7XG4gIHJldHVybiBwcm9wcy5jaGlsZHJlbjtcbn1cblxuLyoqXG4gKiBJbmplY3RzIGEgcGFpciBvZiBmb2N1cyBndWFyZHMgYXQgdGhlIGVkZ2VzIG9mIHRoZSB3aG9sZSBET00gdHJlZVxuICogdG8gZW5zdXJlIGBmb2N1c2luYCAmIGBmb2N1c291dGAgZXZlbnRzIGNhbiBiZSBjYXVnaHQgY29uc2lzdGVudGx5LlxuICovXG5mdW5jdGlvbiB1c2VGb2N1c0d1YXJkcygpIHtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBlZGdlR3VhcmRzID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnW2RhdGEtcmFkaXgtZm9jdXMtZ3VhcmRdJyk7XG4gICAgZG9jdW1lbnQuYm9keS5pbnNlcnRBZGphY2VudEVsZW1lbnQoJ2FmdGVyYmVnaW4nLCBlZGdlR3VhcmRzWzBdID8/IGNyZWF0ZUZvY3VzR3VhcmQoKSk7XG4gICAgZG9jdW1lbnQuYm9keS5pbnNlcnRBZGphY2VudEVsZW1lbnQoJ2JlZm9yZWVuZCcsIGVkZ2VHdWFyZHNbMV0gPz8gY3JlYXRlRm9jdXNHdWFyZCgpKTtcbiAgICBjb3VudCsrO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChjb3VudCA9PT0gMSkge1xuICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdbZGF0YS1yYWRpeC1mb2N1cy1ndWFyZF0nKS5mb3JFYWNoKChub2RlKSA9PiBub2RlLnJlbW92ZSgpKTtcbiAgICAgIH1cbiAgICAgIGNvdW50LS07XG4gICAgfTtcbiAgfSwgW10pO1xufVxuXG5mdW5jdGlvbiBjcmVhdGVGb2N1c0d1YXJkKCkge1xuICBjb25zdCBlbGVtZW50ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnc3BhbicpO1xuICBlbGVtZW50LnNldEF0dHJpYnV0ZSgnZGF0YS1yYWRpeC1mb2N1cy1ndWFyZCcsICcnKTtcbiAgZWxlbWVudC50YWJJbmRleCA9IDA7XG4gIGVsZW1lbnQuc3R5bGUub3V0bGluZSA9ICdub25lJztcbiAgZWxlbWVudC5zdHlsZS5vcGFjaXR5ID0gJzAnO1xuICBlbGVtZW50LnN0eWxlLnBvc2l0aW9uID0gJ2ZpeGVkJztcbiAgZWxlbWVudC5zdHlsZS5wb2ludGVyRXZlbnRzID0gJ25vbmUnO1xuICByZXR1cm4gZWxlbWVudDtcbn1cblxuY29uc3QgUm9vdCA9IEZvY3VzR3VhcmRzO1xuXG5leHBvcnQge1xuICBGb2N1c0d1YXJkcyxcbiAgLy9cbiAgUm9vdCxcbiAgLy9cbiAgdXNlRm9jdXNHdWFyZHMsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/FocusScope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"FocusScope.useComposedRefs[composedRefs]\": (node)=>setContainer(node)\n    }[\"FocusScope.useComposedRefs[composedRefs]\"]);\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (trapped) {\n                let handleFocusIn2 = {\n                    \"FocusScope.useEffect.handleFocusIn2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const target = event.target;\n                        if (container.contains(target)) {\n                            lastFocusedElementRef.current = target;\n                        } else {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusIn2\"], handleFocusOut2 = {\n                    \"FocusScope.useEffect.handleFocusOut2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const relatedTarget = event.relatedTarget;\n                        if (relatedTarget === null) return;\n                        if (!container.contains(relatedTarget)) {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusOut2\"], handleMutations2 = {\n                    \"FocusScope.useEffect.handleMutations2\": function(mutations) {\n                        const focusedElement = document.activeElement;\n                        if (focusedElement !== document.body) return;\n                        for (const mutation of mutations){\n                            if (mutation.removedNodes.length > 0) focus(container);\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleMutations2\"];\n                var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n                document.addEventListener(\"focusin\", handleFocusIn2);\n                document.addEventListener(\"focusout\", handleFocusOut2);\n                const mutationObserver = new MutationObserver(handleMutations2);\n                if (container) mutationObserver.observe(container, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        document.removeEventListener(\"focusin\", handleFocusIn2);\n                        document.removeEventListener(\"focusout\", handleFocusOut2);\n                        mutationObserver.disconnect();\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (container) {\n                focusScopesStack.add(focusScope);\n                const previouslyFocusedElement = document.activeElement;\n                const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n                if (!hasFocusedCandidate) {\n                    const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                    container.dispatchEvent(mountEvent);\n                    if (!mountEvent.defaultPrevented) {\n                        focusFirst(removeLinks(getTabbableCandidates(container)), {\n                            select: true\n                        });\n                        if (document.activeElement === previouslyFocusedElement) {\n                            focus(container);\n                        }\n                    }\n                }\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                        setTimeout({\n                            \"FocusScope.useEffect\": ()=>{\n                                const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                                container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                container.dispatchEvent(unmountEvent);\n                                if (!unmountEvent.defaultPrevented) {\n                                    focus(previouslyFocusedElement ?? document.body, {\n                                        select: true\n                                    });\n                                }\n                                container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                focusScopesStack.remove(focusScope);\n                            }\n                        }[\"FocusScope.useEffect\"], 0);\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"FocusScope.useCallback[handleKeyDown]\": (event)=>{\n            if (!loop && !trapped) return;\n            if (focusScope.paused) return;\n            const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n            const focusedElement = document.activeElement;\n            if (isTabKey && focusedElement) {\n                const container2 = event.currentTarget;\n                const [first, last] = getTabbableEdges(container2);\n                const hasTabbableElementsInside = first && last;\n                if (!hasTabbableElementsInside) {\n                    if (focusedElement === container2) event.preventDefault();\n                } else {\n                    if (!event.shiftKey && focusedElement === last) {\n                        event.preventDefault();\n                        if (loop) focus(first, {\n                            select: true\n                        });\n                    } else if (event.shiftKey && focusedElement === first) {\n                        event.preventDefault();\n                        if (loop) focus(last, {\n                            select: true\n                        });\n                    }\n                }\n            }\n        }\n    }[\"FocusScope.useCallback[handleKeyDown]\"], [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxWSUNUVVNcXERlc2t0b3BcXHRlc3Rfd2ViXFx0ZXN0X3dlYlxcRlJPTlRFTkRcXG5vZGVfbW9kdWxlc1xcQHJhZGl4LXVpXFxyZWFjdC1pZFxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2lkL3NyYy9pZC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xudmFyIHVzZVJlYWN0SWQgPSBSZWFjdFtcInVzZUlkXCIudG9TdHJpbmcoKV0gfHwgKCgpID0+IHZvaWQgMCk7XG52YXIgY291bnQgPSAwO1xuZnVuY3Rpb24gdXNlSWQoZGV0ZXJtaW5pc3RpY0lkKSB7XG4gIGNvbnN0IFtpZCwgc2V0SWRdID0gUmVhY3QudXNlU3RhdGUodXNlUmVhY3RJZCgpKTtcbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWRldGVybWluaXN0aWNJZCkgc2V0SWQoKHJlYWN0SWQpID0+IHJlYWN0SWQgPz8gU3RyaW5nKGNvdW50KyspKTtcbiAgfSwgW2RldGVybWluaXN0aWNJZF0pO1xuICByZXR1cm4gZGV0ZXJtaW5pc3RpY0lkIHx8IChpZCA/IGByYWRpeC0ke2lkfWAgOiBcIlwiKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUlkXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // packages/react/label/src/Label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            props.onMouseDown?.(event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWxhYmVsL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1QjtBQUNHO0FBY3RCO0FBUkosSUFBTSxPQUFPO0FBTWIsSUFBTSxzQkFBYyw4Q0FBcUMsQ0FBQyxPQUFPO0lBQy9ELE9BQ0UsdUVBQUMsZ0VBQVMsQ0FBQyxPQUFWO1FBQ0UsR0FBRztRQUNKLEtBQUs7UUFDTCxhQUFhLENBQUM7WUFFWixNQUFNLFNBQVMsTUFBTTtZQUNyQixJQUFJLE9BQU8sUUFBUSxpQ0FBaUMsRUFBRztZQUV2RCxNQUFNLGNBQWMsS0FBSztZQUV6QixJQUFJLENBQUMsTUFBTSxvQkFBb0IsTUFBTSxTQUFTLEVBQUcsT0FBTSxlQUFlO1FBQ3hFO0lBQUE7QUFHTixDQUFDO0FBRUQsTUFBTSxjQUFjO0FBSXBCLElBQU0sT0FBTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxWSUNUVVNcXERlc2t0b3BcXHRlc3Rfd2ViXFx0ZXN0X3dlYlxcc3JjXFxMYWJlbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZSc7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIExhYmVsXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IE5BTUUgPSAnTGFiZWwnO1xuXG50eXBlIExhYmVsRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5sYWJlbD47XG50eXBlIFByaW1pdGl2ZUxhYmVsUHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5sYWJlbD47XG5pbnRlcmZhY2UgTGFiZWxQcm9wcyBleHRlbmRzIFByaW1pdGl2ZUxhYmVsUHJvcHMge31cblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPExhYmVsRWxlbWVudCwgTGFiZWxQcm9wcz4oKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8UHJpbWl0aXZlLmxhYmVsXG4gICAgICB7Li4ucHJvcHN9XG4gICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgIG9uTW91c2VEb3duPXsoZXZlbnQpID0+IHtcbiAgICAgICAgLy8gb25seSBwcmV2ZW50IHRleHQgc2VsZWN0aW9uIGlmIGNsaWNraW5nIGluc2lkZSB0aGUgbGFiZWwgaXRzZWxmXG4gICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICAgICAgaWYgKHRhcmdldC5jbG9zZXN0KCdidXR0b24sIGlucHV0LCBzZWxlY3QsIHRleHRhcmVhJykpIHJldHVybjtcblxuICAgICAgICBwcm9wcy5vbk1vdXNlRG93bj8uKGV2ZW50KTtcbiAgICAgICAgLy8gcHJldmVudCB0ZXh0IHNlbGVjdGlvbiB3aGVuIGRvdWJsZSBjbGlja2luZyBsYWJlbFxuICAgICAgICBpZiAoIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQgJiYgZXZlbnQuZGV0YWlsID4gMSkgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIH19XG4gICAgLz5cbiAgKTtcbn0pO1xuXG5MYWJlbC5kaXNwbGF5TmFtZSA9IE5BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgUm9vdCA9IExhYmVsO1xuXG5leHBvcnQge1xuICBMYWJlbCxcbiAgLy9cbiAgUm9vdCxcbn07XG5leHBvcnQgdHlwZSB7IExhYmVsUHJvcHMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // packages/react/portal/src/Portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)({\n        \"Portal.useLayoutEffect\": ()=>setMounted(true)\n    }[\"Portal.useLayoutEffect\"], []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ // packages/react/presence/src/Presence.tsx\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                if (node2) stylesRef.current = getComputedStyle(node2);\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/Primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Item,Root,RovingFocusGroup,RovingFocusGroupItem,createRovingFocusGroupScope auto */ // packages/react/roving-focus/src/RovingFocusGroup.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(GROUP_NAME, [\n    createCollectionScope\n]);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeRovingFocusGroup,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: props.__scopeRovingFocusGroup,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, {\n                ...props,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n    const [currentTabStopId = null, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n        prop: currentTabStopIdProp,\n        defaultProp: defaultCurrentTabStopId,\n        onChange: onCurrentTabStopIdChange\n    });\n    const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupImpl.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n                return ({\n                    \"RovingFocusGroupImpl.useEffect\": ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus)\n                })[\"RovingFocusGroupImpl.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupImpl.useEffect\"], [\n        handleEntryFocus\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusProvider, {\n        scope: __scopeRovingFocusGroup,\n        orientation,\n        dir: direction,\n        loop,\n        currentTabStopId,\n        onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": (tabStopId)=>setCurrentTabStopId(tabStopId)\n        }[\"RovingFocusGroupImpl.useCallback\"], [\n            setCurrentTabStopId\n        ]),\n        onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setIsTabbingBackOut(true)\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount + 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount - 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n            \"data-orientation\": orientation,\n            ...groupProps,\n            ref: composedRefs,\n            style: {\n                outline: \"none\",\n                ...props.style\n            },\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, ()=>{\n                isClickFocusRef.current = true;\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event)=>{\n                const isKeyboardFocus = !isClickFocusRef.current;\n                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n                    event.currentTarget.dispatchEvent(entryFocusEvent);\n                    if (!entryFocusEvent.defaultPrevented) {\n                        const items = getItems().filter((item)=>item.focusable);\n                        const activeItem = items.find((item)=>item.active);\n                        const currentItem = items.find((item)=>item.id === currentTabStopId);\n                        const candidateItems = [\n                            activeItem,\n                            currentItem,\n                            ...items\n                        ].filter(Boolean);\n                        const candidateNodes = candidateItems.map((item)=>item.ref.current);\n                        focusFirst(candidateNodes, preventScrollOnEntryFocus);\n                    }\n                }\n                isClickFocusRef.current = false;\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, ()=>setIsTabbingBackOut(false))\n        })\n    });\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, ...itemProps } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupItem.useEffect\": ()=>{\n            if (focusable) {\n                onFocusableItemAdd();\n                return ({\n                    \"RovingFocusGroupItem.useEffect\": ()=>onFocusableItemRemove()\n                })[\"RovingFocusGroupItem.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupItem.useEffect\"], [\n        focusable,\n        onFocusableItemAdd,\n        onFocusableItemRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span, {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!focusable) event.preventDefault();\n                else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, ()=>context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (event.key === \"Tab\" && event.shiftKey) {\n                    context.onItemShiftTab();\n                    return;\n                }\n                if (event.target !== event.currentTarget) return;\n                const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n                if (focusIntent !== void 0) {\n                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item)=>item.focusable);\n                    let candidateNodes = items.map((item)=>item.ref.current);\n                    if (focusIntent === \"last\") candidateNodes.reverse();\n                    else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                        if (focusIntent === \"prev\") candidateNodes.reverse();\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                }\n            })\n        })\n    });\n});\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n    ArrowLeft: \"prev\",\n    ArrowUp: \"prev\",\n    ArrowRight: \"next\",\n    ArrowDown: \"next\",\n    PageUp: \"first\",\n    Home: \"first\",\n    PageDown: \"last\",\n    End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n    if (dir !== \"rtl\") return key;\n    return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n    const key = getDirectionAwareKey(event.key, dir);\n    if (orientation === \"vertical\" && [\n        \"ArrowLeft\",\n        \"ArrowRight\"\n    ].includes(key)) return void 0;\n    if (orientation === \"horizontal\" && [\n        \"ArrowUp\",\n        \"ArrowDown\"\n    ].includes(key)) return void 0;\n    return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus({\n            preventScroll\n        });\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-separator/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/separator/src/Separator.tsx\n\n\n\nvar NAME = \"Separator\";\nvar DEFAULT_ORIENTATION = \"horizontal\";\nvar ORIENTATIONS = [\"horizontal\", \"vertical\"];\nvar Separator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  const ariaOrientation = orientation === \"vertical\" ? orientation : void 0;\n  const semanticProps = decorative ? { role: \"none\" } : { \"aria-orientation\": ariaOrientation, role: \"separator\" };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.div,\n    {\n      \"data-orientation\": orientation,\n      ...semanticProps,\n      ...domProps,\n      ref: forwardedRef\n    }\n  );\n});\nSeparator.displayName = NAME;\nfunction isValidOrientation(orientation) {\n  return ORIENTATIONS.includes(orientation);\n}\nvar Root = Separator;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-tabs/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTabsScope: () => (/* binding */ createTabsScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Content,List,Root,Tabs,TabsContent,TabsList,TabsTrigger,Trigger,createTabsScope auto */ // packages/react/tabs/src/Tabs.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar TABS_NAME = \"Tabs\";\nvar [createTabsContext, createTabsScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(TABS_NAME, [\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.createRovingFocusGroupScope\n]);\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.createRovingFocusGroupScope)();\nvar [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);\nvar Tabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTabs, value: valueProp, onValueChange, defaultValue, orientation = \"horizontal\", dir, activationMode = \"automatic\", ...tabsProps } = props;\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        onChange: onValueChange,\n        defaultProp: defaultValue\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabsProvider, {\n        scope: __scopeTabs,\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__.useId)(),\n        value,\n        onValueChange: setValue,\n        orientation,\n        dir: direction,\n        activationMode,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n            dir: direction,\n            \"data-orientation\": orientation,\n            ...tabsProps,\n            ref: forwardedRef\n        })\n    });\n});\nTabs.displayName = TABS_NAME;\nvar TAB_LIST_NAME = \"TabsList\";\nvar TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        orientation: context.orientation,\n        dir: context.dir,\n        loop,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n            role: \"tablist\",\n            \"aria-orientation\": context.orientation,\n            ...listProps,\n            ref: forwardedRef\n        })\n    });\n});\nTabsList.displayName = TAB_LIST_NAME;\nvar TRIGGER_NAME = \"TabsTrigger\";\nvar TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !disabled,\n        active: isSelected,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            role: \"tab\",\n            \"aria-selected\": isSelected,\n            \"aria-controls\": contentId,\n            \"data-state\": isSelected ? \"active\" : \"inactive\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            id: triggerId,\n            ...triggerProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                    context.onValueChange(value);\n                } else {\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if ([\n                    \" \",\n                    \"Enter\"\n                ].includes(event.key)) context.onValueChange(value);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                const isAutomaticActivation = context.activationMode !== \"manual\";\n                if (!isSelected && !disabled && isAutomaticActivation) {\n                    context.onValueChange(value);\n                }\n            })\n        })\n    });\n});\nTabsTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"TabsContent\";\nvar TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isSelected);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TabsContent.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"TabsContent.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"TabsContent.useEffect.rAF\"]);\n            return ({\n                \"TabsContent.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"TabsContent.useEffect\"];\n        }\n    }[\"TabsContent.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || isSelected,\n        children: ({ present })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n                \"data-state\": isSelected ? \"active\" : \"inactive\",\n                \"data-orientation\": context.orientation,\n                role: \"tabpanel\",\n                \"aria-labelledby\": triggerId,\n                hidden: !present,\n                id: contentId,\n                tabIndex: 0,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                    ...props.style,\n                    animationDuration: isMountAnimationPreventedRef.current ? \"0s\" : void 0\n                },\n                children: present && children\n            })\n    });\n});\nTabsContent.displayName = CONTENT_NAME;\nfunction makeTriggerId(baseId, value) {\n    return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n    return `${baseId}-content-${value}`;\n}\nvar Root2 = Tabs;\nvar List = TabsList;\nvar Trigger = TabsTrigger;\nvar Content = TabsContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFZJQ1RVU1xcRGVza3RvcFxcdGVzdF93ZWJcXHRlc3Rfd2ViXFxGUk9OVEVORFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1jYWxsYmFjay1yZWZcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2VDYWxsYmFja1JlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-controllable-state/src/useControllableState.tsx\n\n\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFZJQ1RVU1xcRGVza3RvcFxcdGVzdF93ZWJcXHRlc3Rfd2ViXFxGUk9OVEVORFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1lc2NhcGUta2V5ZG93blxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1lc2NhcGUta2V5ZG93bi9zcmMvdXNlRXNjYXBlS2V5ZG93bi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWZcIjtcbmZ1bmN0aW9uIHVzZUVzY2FwZUtleWRvd24ob25Fc2NhcGVLZXlEb3duUHJvcCwgb3duZXJEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50KSB7XG4gIGNvbnN0IG9uRXNjYXBlS2V5RG93biA9IHVzZUNhbGxiYWNrUmVmKG9uRXNjYXBlS2V5RG93blByb3ApO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwiRXNjYXBlXCIpIHtcbiAgICAgICAgb25Fc2NhcGVLZXlEb3duKGV2ZW50KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICAgIHJldHVybiAoKSA9PiBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgfSwgW29uRXNjYXBlS2V5RG93biwgb3duZXJEb2N1bWVudF0pO1xufVxuZXhwb3J0IHtcbiAgdXNlRXNjYXBlS2V5ZG93blxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVklDVFVTXFxEZXNrdG9wXFx0ZXN0X3dlYlxcdGVzdF93ZWJcXEZST05URU5EXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWxheW91dC1lZmZlY3RcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlTGF5b3V0RWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IEJvb2xlYW4oZ2xvYmFsVGhpcz8uZG9jdW1lbnQpID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/usePrevious.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyx5Q0FBWSxHQUFHLHdCQUF3QjtBQUNyRCxTQUFTLDBDQUFhO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFZJQ1RVU1xcRGVza3RvcFxcdGVzdF93ZWJcXHRlc3Rfd2ViXFxGUk9OVEVORFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1wcmV2aW91c1xcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1wcmV2aW91cy9zcmMvdXNlUHJldmlvdXMudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZVByZXZpb3VzKHZhbHVlKSB7XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZih7IHZhbHVlLCBwcmV2aW91czogdmFsdWUgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBpZiAocmVmLmN1cnJlbnQudmFsdWUgIT09IHZhbHVlKSB7XG4gICAgICByZWYuY3VycmVudC5wcmV2aW91cyA9IHJlZi5jdXJyZW50LnZhbHVlO1xuICAgICAgcmVmLmN1cnJlbnQudmFsdWUgPSB2YWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIHJlZi5jdXJyZW50LnByZXZpb3VzO1xuICB9LCBbdmFsdWVdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZVByZXZpb3VzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/useSize.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ })

};
;